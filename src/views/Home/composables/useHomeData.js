import { ref, nextTick } from 'vue'
import { throttle, compact } from 'lodash-es'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList, getPartionList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import { closeToast, showLoadingToast } from 'vant'
import { queryZqInfo } from '@/utils/zqInfo'
import { zqQuerySimplified } from '@/api/interface/zq'

export function useHomeData() {
  // 通用数据状态
  const headerBannerList = ref([])
  const gridMenuItems = ref([])
  const skeletonStates = ref({
    banner: true,
    gridMenu: true,
    waterfall: true
  })

  const moduleDataReady = ref({
    banner: false,
    gridMenu: false,
    waterfall: false
  })

  // 瀑布流数据状态
  const waterfallGoodsList = ref([])
  const waterfallLoading = ref(false)
  const waterfallFinished = ref(false)
  const waterfallCurrentPage = ref(1)
  const waterfallPageSize = ref(10)
  const waterfallButtonCanShow = ref(false)
  const waterfallRenderComplete = ref(false)

  // 通用工具函数
  const channelFilterd = (list) => {
    if (isUnicom) {
      return list.filter(item => item.channelType === '1')
    } else if (isWopay) {
      return list.filter(item => item.channelType === '0')
    } else {
      return list.filter(item => item.channelType === '2')
    }
  }

  const transformGoodsData = (item) => ({
    name: item.name || item.goodName,
    price: (item.skuList[0].price) || item.goodsPrice,
    sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
    goodsId: item.id || item.goodsId,
    image: item.listImageUrl || item.image,
    spec: compact([
      item.skuList?.[0]?.param,
      item.skuList?.[0]?.param1,
      item.skuList?.[0]?.param2,
      item.skuList?.[0]?.param3,
      item.skuList?.[0]?.param4
    ]).join(' ') || item.spec || item.goodsSpec,
  })


  // 政企商城商品数据转换
  const transformZqGoodsData = (item) => ({
    name: item.name || item.goodName,
    price: item.skuList?.[0]?.price != null ? item.skuList[0].price : item.goodsPrice,
    sales: item.skuList?.[0]?.realSaleVolume || item.salesCount || 0,
    goodsId: item.id || item.goodsId,
    image: item.listImageUrl || item.image,
    spec: compact([
      item.skuList?.[0]?.param,
      item.skuList?.[0]?.param1,
      item.skuList?.[0]?.param2,
      item.skuList?.[0]?.param3,
      item.skuList?.[0]?.param4
    ]).join(' ') || item.spec || item.goodsSpec,
    lowPrice: item.skuList?.[0]?.lowPrice != null ? item.skuList?.[0]?.lowPrice : '',
    highPrice: item.skuList?.[0]?.highPrice != null ? item.skuList?.[0]?.highPrice : '',
  })

  // 是否需要显示首次加载的 loading 提示
  const shouldShowInitialToast = (isLoadMore) => {
    const isFirstLoad = waterfallGoodsList.value.length === 0
    return !isLoadMore && isFirstLoad && skeletonStates.value.waterfall
  }

  // 构建政企商城查询参数（支持可选 store）
  const buildZqParams = (id, sortType = '', store = null) => {
    const bizCode = getBizCode('GOODS')
    const zqInfo = queryZqInfo()
    console.warn(12313,zqInfo)
    let finalId = id
    let supplierCode = zqInfo.roleType !== '4' ? (zqInfo.isvList?.[0]?.isvId || '') : ''
    let proStr = zqInfo.roleType !== '4' ? (zqInfo.provinceCode?.join(',') || '') : ''
    let type = 2

    if (zqInfo.roleType === '4') {
      // roleType 为 4 时，优先从 store 取值；若无 store，保持为空
      finalId = ''
      if (store) {
        // 直接从 store 获取选中的值
        supplierCode = store.selectedIsvId || ''
        proStr = store.selectedAreaId || ''
      }
      type = 3
    }

    const params = {
      roleType: zqInfo.roleType,
      type,
      bizCode,
      id: finalId,
      supplierCode,
      proStr,
      pageNo: waterfallCurrentPage.value,
      pageSize: waterfallPageSize.value,
    }

    if (sortType) params.price_sort = sortType
    return params
  }

  // 构建普通商城参数
  const buildCommonParams = (id, sortType = '') => {
    const bizCode = getBizCode('GOODS')
    const params = {
      type: 'partion',
      id,
      bizCode,
      page_no: waterfallCurrentPage.value,
      page_size: waterfallPageSize.value,
    }
    if (sortType) params.price_sort = sortType
    return params
  }

  // 统一的瀑布流加载实现（被 getWaterfallList / getWaterfallListWithStore 复用）
  const loadWaterfall = async ({ id, sortType = '', isLoadMore = false, store = null }) => {
    if (waterfallLoading.value || (waterfallFinished.value && isLoadMore)) return

    waterfallLoading.value = true

    const showToast = shouldShowInitialToast(isLoadMore)
    if (showToast) {
      waterfallRenderComplete.value = false
      showLoadingToast()
    }

    const bizCode = getBizCode('GOODS')

    let err, json
    if (bizCode === 'zq') {
      const params = buildZqParams(id, sortType, store)
      ;[err, json] = await zqQuerySimplified(params)
    } else {
      const params = buildCommonParams(id, sortType)
      ;[err, json] = await getGoodsList(params)
    }

    if (showToast) closeToast()

    if (!err && json) {
      const newItems = bizCode === 'zq'
        ? (json.goodsList || []).map(transformZqGoodsData)
        : json.map(transformGoodsData)

      if (isLoadMore) {
        waterfallGoodsList.value = [...waterfallGoodsList.value, ...newItems]
        if (bizCode === 'zq') {
          // 政企商城根据 cacheType 决定是否继续翻页
          if (json.cacheType === '1') {
            waterfallCurrentPage.value++
          }
        } else {
          waterfallCurrentPage.value++
        }
      } else {
        // 平滑替换数据，避免页面跳动
        waterfallGoodsList.value = newItems
        waterfallCurrentPage.value = 2
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }

      // 结束判断
      if (bizCode === 'zq') {
        waterfallFinished.value = json.cacheType === '1' ? false : true
      } else {
        waterfallFinished.value = newItems.length === 0
      }
      waterfallButtonCanShow.value = true
    } else {
      waterfallFinished.value = true
      if (!isLoadMore) {
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }
    }

    waterfallLoading.value = false
  }

  // 骨架屏控制
  const hideSkeletonInOrder = async (moduleOrder = ['banner', 'gridMenu']) => {
    for (const module of moduleOrder) {
      if (moduleDataReady.value[module] && skeletonStates.value[module]) {
        skeletonStates.value[module] = false
        await nextTick()
      }
    }
  }

  // 获取Banner数据
  const getHeaderBannerList = async (showPage = 1) => {
    const [err, json] = await getBannerInfo({
      bizCode: getBizCode('QUERY'),
      showPage
    })

    if (!err) {
      const bannerData = channelFilterd(json).map(item => ({
        type: 'image',
        url: item.imgUrl,
        alt: item.bannerChName,
        linkUrl: item.url,
      }))
      headerBannerList.value = bannerData
    }

    moduleDataReady.value.banner = true
    await hideSkeletonInOrder(['banner'])
  }

  // 获取图标菜单数据
  const getIconList = async (showPage = 2) => {
    const [err, json] = await getIconInfo({
      bizCode: getBizCode('QUERY'),
      channel: curChannelBiz.get(),
      showPage
    })

    if (!err && json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData
    } else {
      gridMenuItems.value = []
    }

    moduleDataReady.value.gridMenu = true
    await hideSkeletonInOrder(['banner', 'gridMenu'])
  }

  // 获取商品列表数据
  const getWaterfallList = async (id, sortType = '', isLoadMore = false) => {
    await loadWaterfall({ id, sortType, isLoadMore })
  }

  // 政企商城专用的瀑布流加载方法（支持store访问）
  const getWaterfallListWithStore = async (id, sortType = '', isLoadMore = false, store = null) => {
    await loadWaterfall({ id, sortType, isLoadMore, store })
  }

  // 加载更多商品
  const handleWaterfallLoadMore = throttle((goodsPoolId) => {
    if (!waterfallFinished.value && !waterfallLoading.value) {
      getWaterfallList(goodsPoolId, '', true)
    }
  }, 300)

  // 重置瀑布流状态
  const resetWaterfallState = () => {
    waterfallGoodsList.value = []
    waterfallCurrentPage.value = 1
    waterfallFinished.value = false
    waterfallLoading.value = false
    waterfallButtonCanShow.value = false
    waterfallRenderComplete.value = false
  }

  // 获取分区列表
  const getPartionListData = async (type = 2) => {
    showLoadingToast()
    const [err, json] = await getPartionList({
      bizCode: getBizCode('GOODS'),
      type
    })
    closeToast()

    if (err) {
      return []
    }

    return json ? json.sort((a, b) => b.pos - a.pos) : []
  }

  return {
    // 数据状态
    headerBannerList,
    gridMenuItems,
    skeletonStates,
    moduleDataReady,
    waterfallGoodsList,
    waterfallLoading,
    waterfallFinished,
    waterfallCurrentPage,
    waterfallPageSize,
    waterfallButtonCanShow,
    waterfallRenderComplete,

    // 工具函数
    channelFilterd,
    transformGoodsData,
    hideSkeletonInOrder,

    // API 方法
    getHeaderBannerList,
    getIconList,
    getWaterfallList,
    getWaterfallListWithStore,
    handleWaterfallLoadMore,
    resetWaterfallState,
    getPartionListData
  }
}
